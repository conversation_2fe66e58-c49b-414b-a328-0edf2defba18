# -------------------------------
# CONFIGURATION
# -------------------------------
$certName = "RWO Certificate"
$subjectCN = "Unvired"
$certFolderRelative = "windows\certificate"      # always inside project root
$pfxFileName = "RWO_Certificate.pfx"
$cerFileName = "RWO_Certificate.cer"
$certPassword = "unvired123*"  # Change this to a strong password

# -------------------------------
# RESOLVE FULL PATHS
# -------------------------------
$currentDir = (Get-Location).Path
$fullCertFolder = Join-Path $currentDir $certFolderRelative
$pfxPath = Join-Path $fullCertFolder $pfxFileName
$cerPath = Join-Path $fullCertFolder $cerFileName

# -------------------------------
# CREATE FOLDER IF NOT EXISTS
# -------------------------------
if (-not (Test-Path $fullCertFolder)) {
    New-Item -ItemType Directory -Path $fullCertFolder -Force
}

# -------------------------------
# CREATE SELF-SIGNED CERTIFICATE
# -------------------------------
$cert = New-SelfSignedCertificate -Type Custom `
    -Subject "CN=$subjectCN" `
    -FriendlyName $certName `
    -KeyUsage DigitalSignature `
    -CertStoreLocation "Cert:\CurrentUser\My"

# -------------------------------
# EXPORT .PFX (private key included)
# -------------------------------
$securePwd = ConvertTo-SecureString -String $certPassword -Force -AsPlainText
Export-PfxCertificate -Cert "Cert:\CurrentUser\My\$($cert.Thumbprint)" `
    -FilePath $pfxPath `
    -Password $securePwd

# -------------------------------
# EXPORT .CER (public certificate)
# -------------------------------
Export-Certificate -Cert "Cert:\CurrentUser\My\$($cert.Thumbprint)" `
    -FilePath $cerPath

# -------------------------------
# TRUST THE CERTIFICATE AUTOMATICALLY
# -------------------------------
Write-Host "Installing public certificate into Trusted People for current user..."
Import-Certificate -FilePath $cerPath -CertStoreLocation Cert:\CurrentUser\TrustedPeople

Write-Host "Certificate generation and installation completed!"
Write-Host "PFX path: $pfxPath"
Write-Host "CER path: $cerPath"
