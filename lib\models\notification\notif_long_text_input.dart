class NotifLongTextInput {
  String title;
  String value;

  NotifLongTextInput({required this.title, required this.value});

  factory NotifLongTextInput.fromJson(Map<String, dynamic> json) {
    return NotifLongTextInput(
      title: json['title'] as String,
      value: json['value'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'value': value,
    };
  }
}
