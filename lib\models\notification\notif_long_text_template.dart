import 'dart:convert';

import 'package:eam/be/C_NOTIF_TEXT_TMPLT_HEADER.dart';
import 'package:eam/be/NOTIF_LONG_TEXT_ADD.dart';
import 'package:flutter/material.dart';

class NotifLongTextTemplateModel {
  List<NotifLongTextTemplateItemModel> items;
  NotifLongTextTemplateModel({required this.items});

  NotifLongTextTemplateModel.fromTemplate(
      C_NOTIF_TEXT_TMPLT_HEADER header, String? longText)
      : items = (longText == null || longText.isEmpty)
            ? List<String>.from(jsonDecode(header.template ?? "[]"))
                .map((heading) => NotifLongTextTemplateItemModel(
                      title: heading.trim(),
                      controller: TextEditingController(text: ""),
                    ))
                .toList()
            : List<Map<String, dynamic>>.from(jsonDecode(longText))
                .map((item) => NotifLongTextTemplateItemModel(
                      title: item['title']?.toString() ?? '',
                      controller: TextEditingController(
                          text: item['value']?.toString() ?? ''),
                    ))
                .toList();

  void dispose() {
    for (var item in items) {
      item.dispose();
    }
  }
}

class NotifLongTextTemplateItemModel {
  String title;
  TextEditingController controller;
  NotifLongTextTemplateItemModel(
      {required this.title, required this.controller});

  void dispose() {
    this.controller.dispose();
  }
}
