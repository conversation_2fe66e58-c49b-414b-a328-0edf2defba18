import 'package:eam/be/C_CODE_GROUP_HEADER.dart';
import 'package:eam/be/C_CODE_HEADER.dart';
import 'package:eam/be/C_NOTIF_TYPE_HEADER.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/models/single_select_model.dart';
import 'package:eam/presentation/eam_packages/dropdown/controller/controller.dart';
import 'package:eam/presentation/eam_packages/dropdown/drop_down_search.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_failure2.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/widgets/single_selection_page.dart';
import 'package:eam/provider/notification/selected_notification_provider.dart';
import 'package:eam/screens/other/single_selection_page.dart';
import 'package:eam/utils/utils.dart';

import 'package:eam/widgets/details_card_widget.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class NotificationDetectionMethod extends StatefulWidget {
  final double SPACE_1 = 8;
  final double SPACE_2 = 16;
  final NotificationModel notificationModel;
  final bool isNewNotification;
  final bool showEditIconFromDbConfig;

  final Function() onSave;
  final Function() onCancel;
  C_NOTIF_TYPE_HEADER? notifTypeHeader;
  NotificationDetectionMethod({
    Key? key,
    required this.notificationModel,
    required this.isNewNotification,
    required this.onSave,
    required this.onCancel,
    required this.notifTypeHeader,
    required this.showEditIconFromDbConfig,
  });
  @override
  State<NotificationDetectionMethod> createState() =>
      _NotificationDetectionMethodState();
}

class _NotificationDetectionMethodState
    extends State<NotificationDetectionMethod> {
  late TextEditingController codeController;
  late TextEditingController codeGroupController;
  bool isEditedMode = false;
  bool isEditable = false;
  late NotificationModel notificationModel;
  bool _loadHeader = false;
  C_NOTIF_TYPE_HEADER? _notifTypeHeader;

  @override
  void initState() {
    if (widget.isNewNotification) {
      isEditedMode = true;
      isEditable = false;
    } else {
      if (widget.showEditIconFromDbConfig == true) {
        isEditable = true;
      } else {
        isEditable = false;
      }
    }
    notificationModel = NotificationModel(
      code: widget.notificationModel.code,
      codeDesc: widget.notificationModel.codeDesc,
      codeGroup: widget.notificationModel.codeGroup,
      codeGroupDesc: widget.notificationModel.codeGroupDesc,
    );
    _initDefaultValues();
    isNotificationHeaderTypeNull(widget.notificationModel.notificationType);
    super.initState();
  }

  @override
  void dispose() {
    codeGroupController.dispose();
    codeController.dispose();
    super.dispose();
  }

  isNotificationHeaderTypeNull(String notificationType) async {
    print(widget.notificationModel.notificationType);
    if (_notifTypeHeader == null) {
      setState(() {
        _loadHeader = true;
      });
      _notifTypeHeader = await NotificationHelper.getNotifTypeHeader(
          notifType: widget.notificationModel.notificationType);

      setState(() {
        _loadHeader = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _loadHeader ? CircularProgressIndicator() : _detctionMethod();
  }

  List<Widget> _children(
    C_NOTIF_TYPE_HEADER typeHeader,
    NotificationModel notificationModel,
    SelectedNotificationProvider selectedNotificationProvider,
  ) {
    return [
      Flexible(
        flex: 1,
        fit: FlexFit.loose,
        child: EamDropDownSearch(
          editable: false,
          title: codeGroupController.text,
          labelName: AppLocalizations.of(context)!.codeGroup,
          dropDownType: DropDownType.activityCodeGroup,
          orderEditIntent: SingleScreenIntent(
            tableName: C_CODE_GROUP_HEADER.TABLE_NAME,
            fieldEquipmentId: widget.notificationModel.equipment,
            fieldFuncLocationId: widget.notificationModel.functionalLocation,
            fieldNotificationType: widget.notificationModel.notificationType,
            fieldCatalogType: typeHeader.cat_type_activity,
          ),
          onTap: () {
            final pro = Provider.of<DropDownController>(context, listen: false);
            if (Utils.isNullOrEmpty(
                    widget.notificationModel.notificationType) ||
                selectedNotificationProvider.selectedNotifActivity != null) {
              pro.showDropdown(false);
              return;
            } else {
              pro.showDropdown(true);
            }
          },
          onChanged: (value) {
            final _dropdownController =
                Provider.of<DropDownController>(context, listen: false);
            final pro = Provider.of<SelectedNotificationProvider>(context,
                listen: false);
            final pro2 = Provider.of<NotificationGeneralNotifier>(context,
                listen: false);
            if (value is SingleSelectModel) {
              // pro.notificationModel.codeGroup = value.id;
              // pro.notificationModel.codeGroupDesc = value.description;
              // pro2.setNotification(pro.notificationModel);
              // context
              //     .read<SelectedNotificationProvider>()
              //     .setfailureMode(notificationModel);
              // _dropdownController.removeString(dropDownType: DropDownType.code);
              pro.setActivityCodeGroupAndDesc(value.id, value.description);
            }
          },
        ),
      ),
      SizedBox(width: 16, height: 16),
      Flexible(
        flex: 1,
        fit: FlexFit.loose,
        child: EamDropDownSearch(
          title: codeController.text,
          dropDownType: DropDownType.activityCode,
          labelName: AppLocalizations.of(context)!.code,
          orderEditIntent: SingleScreenIntent(
            tableName: C_CODE_HEADER.TABLE_NAME,
            fieldCodeGroup:
                selectedNotificationProvider.selectedNotifActivity?.act_codegrp,
            fieldFuncLocationId: notificationModel.functionalLocation,
            fieldCatalogType: typeHeader.cat_type_activity!,
          ),
          onTap: () {
            final pro = Provider.of<DropDownController>(context, listen: false);
            if (Utils.isNullOrEmpty(selectedNotificationProvider
                .selectedNotifActivity?.act_codegrp)) {
              pro.showDropdown(false);
              UIHelper.showSnackBar(context,
                  message: AppLocalizations.of(context)!.selectCodeGroupString);
              return;
            } else {
              pro.showDropdown(true);
            }
          },
          onChanged: (value) {
            if (value is SingleSelectModel) {
              final pro = Provider.of<SelectedNotificationProvider>(context,
                  listen: false);
              pro.setActivityCodeAndDesc(value.id, value.description);
            }
          },
        ),
      ),
    ];
  }

  _detctionMethod() {
    return Consumer<SelectedNotificationProvider>(
      builder: (context, provObj, child) =>
          Consumer<NotificationGeneralNotifier>(
              builder: (context, notifGeneralNotifier, child) {
        _initDefaultValues();
        return notifGeneralNotifier.notifTypeHeader == null ||
                notifGeneralNotifier.notifTypeHeader?.cat_type_coding != "Z"
            ? SizedBox(height: 30)
            : Column(
                children: [
                  CustomCardForTexFields(
                    title: "Detection Method",
                    child: PlatformDetails.isMobileScreen(context)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: _children(
                                notifGeneralNotifier.notifTypeHeader!,
                                notifGeneralNotifier.notification,
                                provObj))
                        : Row(
                            children: _children(
                                notifGeneralNotifier.notifTypeHeader!,
                                notifGeneralNotifier.notification,
                                provObj)),
                  ),
                  SizedBox(height: 30)
                ],
              );
      }),
    );
  }

  void _initDefaultValues() {
    var selectNotifProv = context.read<SelectedNotificationProvider>();

    codeController = TextEditingController(
        text: Utils.formatIdDescription(
            id: selectNotifProv.selectedNotifActivity?.act_code ?? "",
            description:
                selectNotifProv.selectedNotifActivity?.act_code_desc ?? ""));

    codeGroupController = TextEditingController(
        text: Utils.formatIdDescription(
            id: selectNotifProv.selectedNotifActivity?.act_codegrp ?? "",
            description:
                selectNotifProv.selectedNotifActivity?.act_codegrp_desc ?? ""));
  }

  _navigateToSingleSelectionPage({required String type}) async {
    final pro =
        Provider.of<NotificationGeneralNotifier>(context, listen: false);
    if (type == 'CODE_GROUP') {
      if (Utils.isNullOrEmpty(widget.notificationModel.notificationType)) {
        UIHelper.showSnackBar(context,
            message: AppLocalizations.of(context)!.selectNotificationType);
        return;
      }

      if (_notifTypeHeader == null) {
        _notifTypeHeader = await NotificationHelper.getNotifTypeHeader(
            notifType: widget.notificationModel.notificationType);
      }
      print(_notifTypeHeader!.cat_type_coding);
      Navigator.pushNamed(
        context,
        SingleSelectionPage2.routeName,
        arguments: SingleScreenIntent(
          tableName: C_CODE_GROUP_HEADER.TABLE_NAME,
          fieldEquipmentId: widget.notificationModel.equipment,
          fieldFuncLocationId: widget.notificationModel.functionalLocation,
          fieldNotificationType: widget.notificationModel.notificationType,
          fieldCatalogType: _notifTypeHeader!.cat_type_coding!,
        ),
      ).then((value) async {
        if (value is SingleSelectModel) {}
      });
    } else if (type == 'CODE') {
      if (Utils.isNullOrEmpty(widget.notificationModel.codeGroup)) {
        UIHelper.showSnackBar(context,
            message: AppLocalizations.of(context)!.selectCodeGroupString);
        return;
      }
      if (pro.notifTypeHeader != null) {
        Navigator.pushNamed(
          context,
          SingleSelectionPage2.routeName,
          arguments: SingleScreenIntent(
            tableName: C_CODE_HEADER.TABLE_NAME,
            fieldCodeGroup: widget.notificationModel.codeGroup,
            fieldFuncLocationId: widget.notificationModel.functionalLocation,
            fieldCatalogType: pro.notifTypeHeader?.cat_type_coding!,
          ),
        ).then((value) async {
          if (value is SingleSelectModel) {}
        });
      }
    }
  }

  _getCardContent() {
    return Column(
      children: [
        /*Text(
          'Notification No.',
          style: TextStyle(
            fontSize: 16,
            color: AppColor.greySubtitleText,
          ),
        ),
        SizedBox(
          height: 5,
        ),
        Text(
          widget.notificationModel.notificationNo,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).primaryColor,
          ),
        ),*/
        LabelValueWidget(
          label: AppLocalizations.of(context)!.codeGroup,
          isValueEditable: widget.isNewNotification,
          isValueReadOnly: true,
          valueController: codeGroupController,
          onValueTap: () => isEditedMode
              ? _navigateToSingleSelectionPage(type: 'CODE_GROUP')
              : {},
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
        LabelValueWidget(
          label: AppLocalizations.of(context)!.code,
          isValueEditable: widget.isNewNotification,
          isValueReadOnly: true,
          valueController: codeController,
          onValueTap: () =>
              isEditedMode ? _navigateToSingleSelectionPage(type: 'CODE') : {},
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
      ],
    );
  }
}
