import 'package:auto_size_text/auto_size_text.dart';
import 'package:eam/be/FUNCLOC_CLASS.dart';
import 'package:eam/be/FUNCLOC_MEAS_POINT.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/ORDER_COST_SUM.dart';
import 'package:eam/helpers/db_helper.dart';
import 'package:eam/helpers/order_helper.dart';
import 'package:eam/helpers/tech_objects_helper.dart';
import 'package:eam/models/orders/order_cost_model.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/provider/tech_objects/floc_details_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:provider/provider.dart';

class OrderCostView extends StatefulWidget {
  const OrderCostView({Key? key}) : super(key: key);

  @override
  _OrderCostViewState createState() => _OrderCostViewState();
}

class _OrderCostViewState extends State<OrderCostView> {
  final TextStyle _classficationTextStyle =
      const TextStyle(fontWeight: FontWeight.w500, fontSize: 16);

  final generalDivider = Divider(
    color: HexColor("#D5DADD"),
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Consumer<OrderNotifier>(
        builder: (context, prov, child) =>
            FutureBuilder<List<OrderCostSomeModel>>(
          builder: (BuildContext context,
              AsyncSnapshot<List<OrderCostSomeModel>> snapshot) {
            if (snapshot.hasData) {
              return _getTableView(snapshot.data);
            } else if (snapshot.hasError) {
              return Center(
                child: Text(snapshot.error.toString()),
              );
            } else {
              return Center(
                child: CircularProgressIndicator(),
              );
            }
          },
          future: _getOrderCosts(orderNo: prov.orderGeneral.orderNo),
        ),
      ),
    );
  }

  Widget _getTableView(List<OrderCostSomeModel>? orderCosts) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: HexColor("#E5E8EA")),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _classificationTitle(),
            ListView.builder(
              shrinkWrap: true,
              itemCount: orderCosts?.length,
              physics: BouncingScrollPhysics(),
              itemBuilder: (ctx, index) {
                OrderCostSomeModel? orderCost = orderCosts?[index] ?? null;
                return Column(
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: 42,
                      ),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 16),
                                  child: SizedBox(
                                    child: Text(
                                      orderCost?.type ?? "",
                                      style: _classficationTextStyle.copyWith(
                                          fontSize: 15),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: SizedBox(
                                  child: Row(
                                    children: [
                                      Text(
                                        orderCost?.estimated ?? "",
                                        style: _classficationTextStyle.copyWith(
                                            fontSize: 15),
                                        maxLines: 1,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Expanded(
                                child: SizedBox(
                                  child: Row(
                                    children: [
                                      Text(
                                        orderCost?.planned ?? "",
                                        style: _classficationTextStyle.copyWith(
                                            fontSize: 15),
                                        maxLines: 1,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Expanded(
                                child: SizedBox(
                                  child: Row(
                                    children: [
                                      Text(
                                        orderCost?.actual ?? "",
                                        style: _classficationTextStyle.copyWith(
                                            fontSize: 15),
                                        maxLines: 1,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Expanded(
                                child: SizedBox(
                                  child: Row(
                                    children: [
                                      Text(
                                        orderCost?.currency ?? "",
                                        style: _classficationTextStyle.copyWith(
                                            fontSize: 15),
                                        maxLines: 1,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    index + 1 ==
                            context
                                .read<FlocDetailsProvider>()
                                .functionalLocationClass
                                .length
                        ? SizedBox()
                        : Container(
                            width: MediaQuery.of(ctx).size.width,
                            height: 1,
                            color: HexColor("#E5E8EA"),
                          ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _classificationTitle() {
    return Container(
      height: 42,
      decoration: BoxDecoration(
        color: HexColor("#E5E8EA"),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          children: [
            Expanded(
              child: Text(
                "Type",
                style: _classficationTextStyle,
              ),
            ),
            Expanded(
              child: Text(
                "Estimated",
                style: _classficationTextStyle,
              ),
            ),
            Expanded(
              child: Text(
                "Planned",
                style: _classficationTextStyle,
              ),
            ),
            Expanded(
              child: Text(
                "Actual",
                style: _classficationTextStyle,
              ),
            ),
            Expanded(
              child: Text(
                "Currency",
                style: _classficationTextStyle,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<List<OrderCostSomeModel>> _getOrderCosts(
      {required String orderNo}) async {
    return (await OrdersHelper.getOrderCosts(orderNo))
        .map((e) => OrderCostSomeModel(
            type: e.value_category_desc,
            estimated: e.costs_est.toString(),
            planned: e.costs_plan.toString(),
            actual: e.costs_act.toString(),
            currency: e.currency))
        .toList();
  }
}
