import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/models/orders/status_header_model.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/presentation/widgets/molecules_layer/general/general_helper_widgets.dart';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OrderRigInfoCard extends StatefulWidget {
  final double SPACE_1 = 8;
  final double SPACE_2 = 16;
  // final Function(List<CheckBoxStatusHeaderModel>, RadioStatusHeaderModel)
  //     onStatusSave;
  // final Function() onStatusCancel;
  const OrderRigInfoCard({
    Key? key,
    // required this.onStatusSave,
    // required this.onStatusCancel,
  });
  @override
  State<OrderRigInfoCard> createState() => _OrderRigInfoCardState();
}

class _OrderRigInfoCardState extends State<OrderRigInfoCard> {
  bool isEditedMode = false;
  bool isEditable = false;
  late NotificationModel statusModel;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _rigInFoBody();
  }

  _rigInFoBody() {
    return Consumer<OrderNotifier>(
      builder: (context, prov, child) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _titleRow(),
          18.0.spaceY,
          PlatformDetails.isMobileScreen(context) ||
                  PlatformDetails.isTabPortraitScreen(context)
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 45,
                      child: _rigManager(prov),
                    ),
                    24.0.spaceY,
                    Container(
                      height: 45,
                      child: _fieldSuperintendent(prov),
                    ),
                    24.0.spaceY,
                    Container(
                      height: 45,
                      child: _operationManager(prov),
                    ),
                    24.0.spaceY,
                    Container(
                      height: 45,
                      child: _phoneNumber(prov),
                    ),
                    24.0.spaceY,
                    Container(
                      height: 45,
                      child: _rigDirection(prov),
                    ),
                  ],
                )
              : Column(
                  children: [
                    Container(
                      height: 45,
                      child: Row(
                        children: [
                          Expanded(child: _rigManager(prov)),
                          Expanded(child: _fieldSuperintendent(prov)),
                          Expanded(child: _operationManager(prov)),
                          Expanded(child: _phoneNumber(prov)),
                          _checkIsMedium()
                              ? SizedBox()
                              : Expanded(child: SizedBox())
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 42,
                      child: _rigDirection(prov),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  _titleRow() {
    return GeneralTitleRow(
      showEdit: false,
      title: "Rig Info",
      onTapEdit: () {
        Future.delayed(
          Duration.zero,
          () {},
        );
      },
    );
  }

  _rigManager(OrderNotifier notifier) {
    return GeneralItem(
        title: "Rig Manager",
        subtitle: notifier.orderGeneral.rigInfo?.rigManager ?? "");
  }

  _fieldSuperintendent(OrderNotifier notifier) {
    return GeneralItem(
        title: "Field Superintendent",
        subtitle: notifier.orderGeneral.rigInfo?.fieldSuerintendent ?? "");
  }

  _operationManager(OrderNotifier notifier) {
    return GeneralItem(
        title: "Operation Manager",
        subtitle: notifier.orderGeneral.rigInfo?.operatoinManager ?? "");
  }

  _phoneNumber(OrderNotifier notifier) {
    return GeneralItem(
        title: "Phone Number",
        subtitle: notifier.orderGeneral.rigInfo?.phoneNumber ?? "");
  }

  _rigDirection(OrderNotifier notifier) {
    return GeneralItem(
        title: "Rig Direction",
        subtitle: notifier.orderGeneral.rigInfo?.rigDirection ?? "");
  }

  _checkIsMedium() {
    if (MediaQuery.of(context).size.width > 850) {
      return false;
    } else {
      return true;
    }
  }
}
