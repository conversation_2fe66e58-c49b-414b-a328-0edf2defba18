import 'package:eam/be/ORDER_MATERIAL.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/eam_packages/tab_bar/controller.dart';
import 'package:eam/presentation/eam_packages/tab_bar/eam_tab_bar.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/provider/order_detail/order_material_provider.dart';
import 'package:eam/utils/app_color.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import 'material_menu_action_button.dart';

double _quantityHeight = 48;

class MaterialItem extends StatelessWidget {
  final ORDER_MATERIAL materialItem;
  final bool disablePostingQuantityIcon;
  final bool isDelete;
  final bool showPostingQty;
  final Function getMaterialPostingQuantity;
  const MaterialItem({
    super.key,
    required this.materialItem,
    this.disablePostingQuantityIcon = false,
    required this.isDelete,
    required this.getMaterialPostingQuantity,
    required this.showPostingQty,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: LayoutBuilder(builder: (context, constraints) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: constraints.maxWidth,
                child: _firstRow(context, constraints.maxWidth),
              ),
              _threeQtys(context, constraints.maxWidth),
            ],
          );
        }),
      ),
    );
  }

  _resPonsiveWidget(
      BuildContext context, List<Widget> children, double maxWidth) {
    return maxWidth < 800
        ? SizedBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children
                  .map((e) => Column(
                        children: [
                          SizedBox(
                            height: 20,
                          ),
                          e,
                        ],
                      ))
                  .toList(),
            ),
          )
        : Column(
            children: [
              SizedBox(
                height: 16,
              ),
              SizedBox(
                height: _quantityHeight,
                child: Row(
                  children: children.map((e) => Expanded(child: e)).toList(),
                ),
              ),
            ],
          );
  }

  Widget _threeQtys(BuildContext context, double maxWidth) {
    return _resPonsiveWidget(
        context,
        [
          _requiredQty(context),
          _usedQty(context),
          _postingQty(context, maxWidth),
        ],
        maxWidth);
  }

  _firstRow(BuildContext context, double maxWidth) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Container(
            // color: Colors.amber,
            width: double.maxFinite,
            child: Text(
              _getMaterialTitle(materialItem: materialItem, context: context),
              style: AppStyles.headLine16,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        maxWidth <= 800
            ? Consumer<OrderNotifier>(builder: (context, pro, child) {
                return pro.isEditable
                    ? Container(
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              _postingQtyCard(context),
                              materialItem.p_mode == Constants.P_MODE_ADD
                                  ? _deletematerialButton(context)
                                  : SizedBox()
                              // _materialMenuActionButton(context)
                            ]),
                      )
                    : SizedBox();
              })
            : SizedBox()
      ],
    );
  }

  _deletematerialButton(BuildContext context) {
    var isTabPortraitOrMobile =
        TabPortraitOrMobile.isTabPortraitOrMobile(context);
    return BlueBorderButton(
      buttonName: isTabPortraitOrMobile
          ? ""
          : " ${AppLocalizations.of(context)!.deleteString}",
      buttonNameColor: Color.fromRGBO(248, 18, 18, 1),
      onTap: () {
        confirmAndDeleteMaterial(materialItem, context);
      },
      isIconRequired: true,
      isBorderRequired: false,
      icon: EamIcon(
        iconName: EamIcon.delete,
        color: Color.fromRGBO(248, 18, 18, 1),
        height: 20,
      ).icon(),
    );
  }

  _materialMenuActionButton(BuildContext context) {
    return MaterialMenuActionButton(
      onChanged: (String value) {
        if (value == "postingQty") {
          getMaterialPostingQuantity(orderMaterial: materialItem);
        } else if (value == "delete") {
          confirmAndDeleteMaterial(materialItem, context);
        }
      },
      showDelete: materialItem.p_mode == Constants.P_MODE_ADD,
      materialItem: materialItem,
    );
  }

  void confirmAndDeleteMaterial(ORDER_MATERIAL material, BuildContext context) {
    final _materialProvider =
        Provider.of<OrderMaterialProvider>(context, listen: false);
    UIHelper.showEamDialog(context,
        title: AppLocalizations.of(context)!.alertString,
        description:
            AppLocalizations.of(context)!.areYouSureYouWantToDeleteThisMaterial,
        negativeActionLabel: AppLocalizations.of(context)!.cancel,
        positiveActionLabel: AppLocalizations.of(context)!.ok,
        onPositiveClickListener: () async {
      try {
        if (material.p_mode == Constants.P_MODE_ADD) {
          await AppDatabaseManager().delete(
              DBInputEntity(ORDER_MATERIAL.TABLE_NAME, material.toJson()));
          await _materialProvider.getallOrderMaterial(
            orderNo: material.order_no ?? "",
          );
          await _materialProvider.getOperationMaterial(
              orderNo: material.order_no ?? "",
              operationNo: material.operation_no);

          EamTabController().loadWorkOrderTabBadgeCounts(context,
              orderNo: material.order_no ?? "",
              tabBarType: EamTabBarType.workOrder);
          EamTabController().addBadgeCount(context,
              count: _materialProvider.orderMaterialData.length,
              titleText: context.locale.materials,
              tabBarType: EamTabBarType.operation);
          Navigator.of(context, rootNavigator: true).pop();
        } else {
          UIHelper.showSnackBar(context,
              message: AppLocalizations.of(context)!.cannotAbleToDelete);
        }
      } catch (e) {
        Logger.logError(
            'MaterialItem', 'confirmAndDeleteMaterial', e.toString());
        UIHelper.showSnackBar(context,
            message: AppLocalizations.of(context)!.deleteError);
      }
    });
  }

  _requiredQty(BuildContext context) {
    return SizedBox(
      height: _quantityHeight,
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.required,
              style: AppStyles.textStyle_14_400w,
              maxLines: 1,
            ),
            SizedBox(
              height: 8,
            ),
            Text(
              _getRequiredCount2(materialItem: materialItem),
              style: AppStyles.textStyle_14_600w,
              maxLines: 1,
            )
          ],
        ),
      ),
    );
  }

  _usedQty(BuildContext context) {
    return Container(
      height: _quantityHeight,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.usedQuantity,
            style: AppStyles.textStyle_14_400w,
            maxLines: 1,
          ),
          SizedBox(
            height: 8,
          ),
          Text(
            _getUsedQuantity2(materialItem: materialItem),
            style: AppStyles.textStyle_14_600w,
            maxLines: 1,
          )
        ],
      ),
    );
  }

  _postingQty(BuildContext context, double maxWidth) {
    return SizedBox(
      height: _quantityHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Container(
              // color: Colors.yellow,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context)!.postingQuantity,
                    style: AppStyles.textStyle_14_400w,
                    maxLines: 1,
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  Text(
                    _getPostingQuantity2(materialItem: materialItem),
                    style: AppStyles.textStyle_14_600w,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ),
          maxWidth > 800
              ? Expanded(
                  child: Container(
                    child:
                        Consumer<OrderNotifier>(builder: (context, pro, child) {
                      return pro.isEditable
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                _postingQtyCard(context),
                                materialItem.p_mode == Constants.P_MODE_ADD
                                    ? _deletematerialButton(context)
                                    : SizedBox()
                              ],
                            )
                          : SizedBox();
                    }),
                  ),
                )
              : SizedBox()
        ],
      ),
    );
  }

  _postingQtyCard(BuildContext context) {
    return showPostingQty
        ? InkResponse(
            onTap: disablePostingQuantityIcon
                ? () {
                    getMaterialPostingQuantity(orderMaterial: materialItem);
                  }
                : null,
            child: EamIcon(
                    iconName: EamIcon.shopping_card,
                    color: Theme.of(context).primaryColor)
                .icon())
        : SizedBox();
  }

  String _getMaterialTitle(
      {required ORDER_MATERIAL materialItem, required BuildContext context}) {
    if (materialItem.material_desc!.isNotEmpty) {
      if (PlatformDetails.isMobileScreen(context) ||
          PlatformDetails.isTabPortraitScreen(context)) {
        return "${materialItem.material}\n${materialItem.material_desc} ";
      } else {
        return "${materialItem.material} - ${materialItem.material_desc} ";
      }
    }
    return materialItem.material.toString();
  }

  // String _getMaterialTitle({required ORDER_MATERIAL materialItem}) {
  //   if (materialItem.material_desc!.isNotEmpty) {
  //     return "${materialItem.material} - ${materialItem.material_desc} ";
  //   }
  //   return materialItem.material.toString();
  // }

  _getRequiredCount2({required ORDER_MATERIAL materialItem}) {
    // return widget.orderMaterial.operation_no;
    return '${materialItem.required_quantity} ${materialItem.uom}';
  }

  _getUsedQuantity2({required ORDER_MATERIAL materialItem}) {
    return '${materialItem.withdrawal_quantity ?? 0.0} ${materialItem.uom}';
  }

  _getPostingQuantity2({required ORDER_MATERIAL materialItem}) {
    return '${materialItem.posting_quantity ?? 0.0} ${materialItem.uom}';
  }

  _getRequiredCount(BuildContext context,
      {required ORDER_MATERIAL materialItem}) {
    return Text(
      '${AppLocalizations.of(context)!.required} ${materialItem.required_quantity} ${materialItem.uom}',
      //widget.order.orderHeader!.fUNCLOCDESC,
      style: ScreenUtils.getCardSubtitleTextStyle(),
    );
  }

  _getUsedQuantity(BuildContext context,
      {required ORDER_MATERIAL materialItem}) {
    return Text(
      '${AppLocalizations.of(context)!.usedQuantity} ${materialItem.withdrawal_quantity ?? 0.0} ${materialItem.uom}',
      //widget.order.orderHeader!.fUNCLOCDESC,
      style: ScreenUtils.getCardSubtitleTextStyle(),
    );
  }

  _getPostingQuantity(BuildContext context,
      {required ORDER_MATERIAL materialItem}) {
    return Column(
      children: [
        SizedBox(
          height: 4,
        ),
        Text(
          '${AppLocalizations.of(context)!.postingQuantity} ${materialItem.posting_quantity ?? 0.0} ${materialItem.uom}',
          style: ScreenUtils.getCardSubtitleTextStyle(),
        ),
      ],
    );
  }

  _getPostingQuantityButton(BuildContext context,
      {required ORDER_MATERIAL material}) {
    return InkResponse(
      onTap: disablePostingQuantityIcon
          ? () {
              getMaterialPostingQuantity(orderMaterial: material);
            }
          : null,
      child: Container(
        height: 48,
        width: 48,
        child: Icon(
          Icons.add_shopping_cart,
          color: disablePostingQuantityIcon
              ? Theme.of(context).primaryColor
              : Colors.grey,
        ),
      ),
    );
  }

  _getMaterialDesc({required ORDER_MATERIAL material}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '${material.material}',
          //widget.order.orderHeader!.fUNCLOCDESC,
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        if (material.material_desc!.isNotEmpty) ...[
          Flexible(
            child: Text(
              " - ${material.material_desc} ",
              style: TextStyle(
                //color: AppColor.blackTitleText,
                //color: Theme.of(context).primaryColor,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
