import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/EQUIP_INPUT_HEADER.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/FUNC_LOC_INPUT_HEADER.dart';
import 'package:eam/be/MATERIAL_HEADER.dart';
import 'package:eam/be/MATERIAL_PLANT_SLOC.dart';
import 'package:eam/be/NOTIF_HEADER.dart';
import 'package:eam/be/NOTIF_SEARCH_HEADER.dart';
import 'package:eam/be/ORDER_HEADER.dart';
import 'package:eam/be/ORDER_MATERIAL.dart';
import 'package:eam/be/ORDER_OBJECT_LIST.dart';
import 'package:eam/be/ORDER_OPERATIONS.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/material_search_helper.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/order_helper.dart';
import 'package:eam/helpers/pa_helper.dart';
import 'package:eam/helpers/tech_objects_helper.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/common_widgets/inkwell.dart';
import 'package:eam/presentation/eam_packages/tab_bar/controller.dart';
import 'package:eam/presentation/eam_packages/tab_bar/eam_tab_bar.dart';

import 'package:eam/presentation/common_widgets/eam_tab.dart';
import 'package:eam/presentation/pages/materials/spare_parts.dart';
import 'package:eam/presentation/pages/notifications/notification_details/notification_details.dart';
import 'package:eam/presentation/pages/tech_objects/equipments/equipment_details_page.dart';
import 'package:eam/presentation/pages/tech_objects/floc/floc_details/floc_details_page.dart';
import 'package:eam/presentation/pages/widgets/atoms_layer/molecules_layer/buttons.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';

import 'package:eam/presentation/pages/work_orders/work_orderdetails/widgets/material/widget/add_spare_parts_button.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/widgets/material/widget/material_item.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/widgets/material/widget/order_material_list_item.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/widgets/material/widget/scanner_.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/widgets/operation/operation_detail/add_spareparts_helper.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/provider/material/material_list_provider.dart';
import 'package:eam/provider/notification/selected_notification_provider.dart';
import 'package:eam/provider/order/order_object_list_provider.dart';
import 'package:eam/provider/order/selected_order_provider.dart';

import 'package:eam/provider/order_detail/order_material_provider.dart';
import 'package:eam/provider/selected_operation_provider.dart';
import 'package:eam/screens/materials/material_list_page.dart';
import 'package:eam/screens/notification/add_edit_notification_page.dart';
import 'package:eam/screens/notification/add_edit_notification_page2.dart';
import 'package:eam/screens/orders/order_detail/material/order_material_detail_page.dart';
import 'package:eam/screens/param/route_param.dart';

import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/enum.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/common_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/Logger.dart';
import 'package:provider/provider.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

class OrderObjectListPage extends StatefulWidget {
  const OrderObjectListPage({
    Key? key,
  }) : super(key: key);

  @override
  _OrderObjectListPageState createState() => _OrderObjectListPageState();
}

var orderMaterialNavigationKey = GlobalKey<NavigatorState>();

class _OrderObjectListPageState extends State<OrderObjectListPage> {
  List _tabItems = ["Equipement", "FLoc", "Notification"];
  bool _init = true;
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _init ? _getTabView() : Center(child: CircularProgressIndicator()),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _getTabView() {
    return Column(
      children: [
        const SizedBox(
          height: 8,
        ),
        _firstRow(),
        const SizedBox(
          height: 26,
        ),
        Consumer<OrderObjectListProvider>(builder: (context, prov, child) {
          switch (prov.currentView) {
            case OrderObjectListViewType.equipement:
              return _equipement();
            case OrderObjectListViewType.floc:
              return _floc();
            case OrderObjectListViewType.notification:
              return _notification();
          }
        })
      ],
    );
  }

  _equipement() {
    return Consumer<OrderNotifier>(
        builder: (context, orderNotifier, child) => FutureBuilder(
            future: OrdersHelper.getOrderObjectListByOrderNoAndType(
                orderNotifier.orderGeneral.orderNo,
                OrderObjectListViewType.equipement),
            builder: ((context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              }
              if (snapshot.hasError) {
                return Text('Error: ${snapshot.error}');
              }
              final data = snapshot.data;
              final length = data is List ? data?.length : 0;
              return ListView.builder(
                  shrinkWrap: true,
                  itemCount: length,
                  itemBuilder: (context, index) => _buildEquipmentItem(
                      orderObjectList: data?[index] ?? null));
            })));
  }

  _floc() {
    return Consumer<OrderNotifier>(
        builder: (context, orderNotifier, child) => FutureBuilder(
            future: OrdersHelper.getOrderObjectListByOrderNoAndType(
                orderNotifier.orderGeneral.orderNo,
                OrderObjectListViewType.floc),
            builder: ((context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              }
              if (snapshot.hasError) {
                return Text('Error: ${snapshot.error}');
              }
              final data = snapshot.data;
              final length = data is List ? data?.length : 0;
              return ListView.builder(
                  shrinkWrap: true,
                  itemCount: length,
                  itemBuilder: (context, index) =>
                      _buildFlocItem(orderObjectList: data?[index] ?? null));
            })));
  }

  _notification() {
    return Consumer<OrderNotifier>(
        builder: (context, orderNotifier, child) => FutureBuilder(
            future: OrdersHelper.getOrderObjectListByOrderNoAndType(
                orderNotifier.orderGeneral.orderNo,
                OrderObjectListViewType.notification),
            builder: ((context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              }
              if (snapshot.hasError) {
                return Text('Error: ${snapshot.error}');
              }
              final data = snapshot.data;
              final length = data is List ? data?.length : 0;
              return ListView.builder(
                  shrinkWrap: true,
                  itemCount: length,
                  itemBuilder: (context, index) =>
                      _buildNotifItem(orderObjectList: data?[index] ?? null));
              ;
            })));
  }

  _firstRow() {
    return Row(
      children: [
        _tabs(),
        Spacer(),
        SizedBox(
          width: 20,
        ),
      ],
    );
  }

  _tabs() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: EamTabs(
        items: _tabItems,
        height: 46,
        onChanged: (i, status) {
          context.read<OrderObjectListProvider>().setCurrentView(status);
        },
      ),
    );
  }

  _getTitle(String title) {
    return Visibility(
        visible: !Utils.isNullOrEmpty(title),
        child: Text(title,
            style: AppStyles.textStyle_14_700w.copyWith(
              overflow: TextOverflow.ellipsis,
            )));
  }

  _getSubTitle(String subTitle) {
    return Visibility(
        visible: !Utils.isNullOrEmpty(subTitle),
        child: Text(subTitle,
            style: AppStyles.textStyle_14_500w.copyWith(
              overflow: TextOverflow.ellipsis,
            )));
  }

  void _navigateToEquipmentDetails({required String equipNo}) async {
    EQUIP_HEADER? equip_header =
        await TechObjectsHelper.getEquipmentHeaderForDetail(equipNo: equipNo);

    Navigator.of(context).pushNamed(
      EquipmentDetailsPage.routeName,
      arguments: EquipmentParam(eqno: equip_header!.equnr!),
    );
  }

  Future<void> downloadEquipment(
      {required BuildContext context, required String equipNo}) async {
    try {
      EQUIP_INPUT_HEADER equipInputHeader =
          new EQUIP_INPUT_HEADER(equip_no: equipNo);
      // equipInputHeader.equip_no = equipNo;
      await context
          .read<OrderNotifier>()
          .downloadEquipment(equipNo: equipNo, context: context);
    } catch (e) {
      Logger.logError(
          "FlocDetailSubItems", 'downloadFunctionalLocation', e.toString());
    }
  }

  _buildEquipmentItem({required ORDER_OBJECT_LIST? orderObjectList}) {
    return Container(
      decoration: AppStyles.deFaultBoxDecoration,
      child: InkWellCard(
        borderRadius: 8,
        onTap: () async {
          bool? existsInDb = await TechObjectsHelper.isSubEquipmentExistsInDB(
              equipNo: orderObjectList?.equip_no ?? "");
          if (existsInDb!) {
            _navigateToEquipmentDetails(
                equipNo: orderObjectList?.equip_no ?? "");
          } else {
            await downloadEquipment(
              context: context,
              equipNo: orderObjectList?.equip_no ?? "",
            ).then((value) {
              _navigateToEquipmentDetails(
                  equipNo: orderObjectList?.equip_no ?? "");
            });
          }
        },
        child: ListTile(
          leading: EamIcon(iconName: EamIcon.equip).icon(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 8,
              ),
              _getTitle(orderObjectList?.equip_no ?? ""),
              SizedBox(
                height: 8,
              ),
              _getSubTitle(orderObjectList?.equip_desc ?? ""),
              SizedBox(
                height: 8,
              ),
            ],
          ),
          trailing:
              EamIcon(iconName: EamIcon.arrow_forward, height: 16, width: 16)
                  .icon(),
        ),
      ),
    );
  }

  Future<void> _navigateToFunctionalLocDetails(
      {required String funcLoc}) async {
    FUNC_LOC_HEADER? funcLocHeader =
        await TechObjectsHelper.getFLocHeader(fLocVal: funcLoc);

    Navigator.of(context).pushNamed(FlocDetailsPage.routeName,
        arguments: funcLocHeader!.func_loc!);
  }

  Future<void> downloadFunctionalLocation(
      {required BuildContext context, required String funcLocation}) async {
    try {
      FUNC_LOC_INPUT_HEADER funcLocInputHeader =
          new FUNC_LOC_INPUT_HEADER(fl_val: funcLocation);
      // funcLocInputHeader.fl_val = funcLocation;
      await context
          .read<OrderNotifier>()
          .downloadFunctionalLocation(floc: funcLocation, context: context)
          .then((value) {
        _navigateToFunctionalLocDetails(funcLoc: funcLocation);
      });
    } catch (e) {
      Logger.logError(
          "FlocDetailSubItems", 'downloadFunctionalLocation', e.toString());
    }
  }

  _buildFlocItem({required ORDER_OBJECT_LIST? orderObjectList}) {
    return Container(
      decoration: AppStyles.deFaultBoxDecoration,
      child: InkWellCard(
        borderRadius: 8,
        onTap: () async {
          bool? existsInDb =
              await TechObjectsHelper.isSubFunctionalLocExistsInDB(
                  subFLoc: orderObjectList?.func_loc ?? "");
          if (existsInDb!) {
            _navigateToFunctionalLocDetails(
                funcLoc: orderObjectList?.func_loc ?? "");
          } else {
            downloadFunctionalLocation(
              context: context,
              funcLocation: orderObjectList?.func_loc ?? "",
            );
          }
        },
        child: ListTile(
          leading: EamIcon(iconName: EamIcon.location).icon(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 8,
              ),
              _getTitle(orderObjectList?.func_loc ?? ""),
              SizedBox(
                height: 8,
              ),
              _getSubTitle(orderObjectList?.func_loc_desc ?? ""),
              SizedBox(
                height: 8,
              ),
            ],
          ),
          trailing:
              EamIcon(iconName: EamIcon.arrow_forward, height: 16, width: 16)
                  .icon(),
        ),
      ),
    );
  }

  _navigateToNotificationDetail(String notifNo) async {
    if (Utils.isNullOrEmpty(notifNo)) {
      return;
    }

    NOTIF_HEADER? notifHeader =
        await NotificationHelper.getNotifHeader(notifNo: notifNo);

    if (notifHeader == null) {
      await downloadNotification(notifNo: notifNo);
      return;
    } else {
      Provider.of<SelectedNotificationProvider>(context, listen: false)
        ..modeType = AddEditNotificationPage.NOTIFICATION_MODE_EDIT
        ..selectedNotifNo = notifNo
        ..selectedNotifTypeHeader = null;
      NavigationService.pushNamed(NotificationDetailPage2.routeName,
          arguments: NotificationParam(
              type: AddEditNotificationPage2.NOTIFICATION_MODE_ADD));
    }
  }

  Future<void> downloadNotification({required String notifNo}) async {
    try {
      NOTIF_SEARCH_HEADER searchHeader = NOTIF_SEARCH_HEADER(
        notif_no: notifNo,
      );

      await getNotification(searchHeader: searchHeader);
    } catch (e) {
      Logger.logError('sourceClass', 'sourceMethod', e.toString());
    }
  }

  Future<void> getNotification(
      {required NOTIF_SEARCH_HEADER searchHeader}) async {
    try {
      UIHelper.showEamProgressDialog2(
        context,
        title: AppLocalizations.of(context)!.refreshInitiatedString,
        showCancelIcon: false,
        barrierDismissible: false,
      );
      Result result = await PAHelper.getQueryNotificationInSyncMode(
        searchHeader: searchHeader,
      );
      if (result.statusCode == Status.httpBadRequest ||
          result.statusCode == Status.httpNotFound) {
        //  Navigator.of(context, rootNavigator: true).pop();
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
          UIHelper.showEamDialog2(
            context,
            title: AppLocalizations.of(context)!.alertString,
            description: result.body['InfoMessage'][0]['message'],
            dismissible: false,
            positiveActionLabel: AppLocalizations.of(context)!.okayString,
            onPositiveClickListener: () => UIHelper.closeDialog(context),
          );
        }
      } else if (result.statusCode == Status.httpOk ||
          result.statusCode == Status.httpCreated) {
        //  Navigator.of(context, rootNavigator: true).pop();

        if (result.body['InfoMessage'] != null) {
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
            UIHelper.showEamDialog2(
              context,
              title: AppLocalizations.of(context)!.alertString,
              description: result.body['InfoMessage'][0]['message'],
              dismissible: false,
              positiveActionLabel: AppLocalizations.of(context)!.okayString,
              onPositiveClickListener: () => UIHelper.closeDialog(context),
            );
          } else {
            // UIHelper.showSnackBar(context, message: result.body['InfoMessage'][0]['message']);
            UIHelper.showEamDialog2(
              context,
              title: AppLocalizations.of(context)!.infoString,
              description: result.body['InfoMessage'][0]['message'],
              dismissible: false,
              positiveActionLabel: AppLocalizations.of(context)!.okayString,
              onPositiveClickListener: () {
                UIHelper.closeDialog(context);
              },
            );
          }
        } else {
          UIHelper.closeDialog(context);
          UIHelper.showSnackBar(context,
              message:
                  AppLocalizations.of(context)!.notificationDownloadSuccess);
        }
      }
    } catch (e) {
      UIHelper.closeDialog(context);
      UIHelper.showEamDialog2(
        context,
        title: AppLocalizations.of(context)!.errorString,
        description: e.toString(),
        dismissible: false,
        positiveActionLabel: AppLocalizations.of(context)!.okayString,
        onPositiveClickListener: () {
          UIHelper.closeDialog(context);
        },
      );
    }
  }

  _buildNotifItem({required ORDER_OBJECT_LIST? orderObjectList}) {
    return Container(
      decoration: AppStyles.deFaultBoxDecoration,
      child: InkWellCard(
        borderRadius: 8,
        onTap: () async {
          NOTIF_HEADER? notifHeader = await NotificationHelper.getNotifHeader(
              notifNo: orderObjectList?.notif_no);
          ;
          if (notifHeader != null) {
            _navigateToNotificationDetail(orderObjectList?.notif_no ?? "");
          } else {
            await downloadNotification(notifNo: orderObjectList?.notif_no ?? "")
                .then((value) {
              _navigateToNotificationDetail(orderObjectList?.notif_no ?? "");
            });
          }
        },
        child: ListTile(
          leading: EamIcon(iconName: EamIcon.notification).icon(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 8,
              ),
              _getTitle(orderObjectList?.notif_no ?? ""),
              SizedBox(
                height: 8,
              ),
              _getSubTitle(orderObjectList?.notif_short_txt ?? ""),
              SizedBox(
                height: 8,
              ),
            ],
          ),
          trailing:
              EamIcon(iconName: EamIcon.arrow_forward, height: 16, width: 16)
                  .icon(),
        ),
      ),
    );
  }
}
