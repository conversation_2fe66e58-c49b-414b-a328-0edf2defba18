import 'package:eam/utils/enum.dart';
import 'package:flutter/material.dart';

class OrderObjectListProvider with ChangeNotifier {
  OrderObjectListViewType _currentView = OrderObjectListViewType.equipement;

  OrderObjectListViewType get currentView => _currentView;

  void setCurrentView(String view) {
    var fromString = OrderObjectListViewType.values.firstWhere(
      (e) => e.value == view,
      orElse: () => OrderObjectListViewType.equipement,
    );
    _currentView = fromString;
    notifyListeners();
  }
}
