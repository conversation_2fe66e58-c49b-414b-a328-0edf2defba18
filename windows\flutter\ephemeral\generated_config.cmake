# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\sdk\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Projects_unv\\eam-precision-drilling" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+20" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 20 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\sdk\\flutter\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Projects_unv\\eam-precision-drilling"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\sdk\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Projects_unv\\eam-precision-drilling\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Projects_unv\\eam-precision-drilling"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Projects_unv\\eam-precision-drilling\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC81NGE3MTQ1MzAzZjBkZDlkMGY5MzQyNGEyZTEyNGViNGFiZWY1MDkxLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Projects_unv\\eam-precision-drilling\\.dart_tool\\package_config.json"
)
